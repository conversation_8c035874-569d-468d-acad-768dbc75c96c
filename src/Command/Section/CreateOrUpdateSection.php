<?php

namespace App\Command\Section;

final class CreateOrUpdateSection
{

    public function __construct(
        #[Assert\NotBlank]
        private string $sectionTitle,
        #[Assert\NotBlank]
        private string $sectionType,
        private ?array $medicalConditionSections = null,
        private ?array $productSections = null,
        private ?array $generalSections = null,
        private ?array $questionSections = null,
    ) {
    }

    public function getSectionTitle(): string
    {
        return $this->sectionTitle;
    }

    public function setSectionTitle(string $sectionTitle): void
    {
        $this->sectionTitle = $sectionTitle;
    }

    public function getSectionType(): string
    {
        return $this->sectionType;
    }

    public function setSectionType(string $sectionType): void
    {
        $this->sectionType = $sectionType;
    }

    public function getMedicalConditionSections(): ?array
    {
        return $this->medicalConditionSections;
    }

    public function setMedicalConditionSections(?array $medicalConditionSections): void
    {
        $this->medicalConditionSections = $medicalConditionSections;
    }

    public function getProductSections(): ?array
    {
        return $this->productSections;
    }

    public function setProductSections(?array $productSections): void
    {
        $this->productSections = $productSections;
    }

    public function getGeneralSections(): ?array
    {
        return $this->generalSections;
    }

    public function setGeneralSections(?array $generalSections): void
    {
        $this->generalSections = $generalSections;
    }

    public function getQuestionSections(): ?array
    {
        return $this->questionSections;
    }

    public function setQuestionSections(?array $questionSections): void
    {
        $this->questionSections = $questionSections;
    }
}
