parameters:
	ignoreErrors:
		-
			message: "#^Property App\\\\Command\\\\QuestionResponse\\\\File\\:\\:\\$file is never read, only written\\.$#"
			count: 1
			path: src/Command/QuestionResponse/File.php

		-
			message: "#^Attribute class Symfony\\\\Component\\\\Validator\\\\Constraints\\\\IdenticalTo does not have the parameter target\\.$#"
			count: 1
			path: src/Command/QuestionResponse/ImperialBodyMassIndexQuestionResponse.php

		-
			message: "#^Attribute class Symfony\\\\Component\\\\Validator\\\\Constraints\\\\NotBlank does not have the parameter target\\.$#"
			count: 1
			path: src/Command/QuestionResponse/ImperialBodyMassIndexQuestionResponse.php

		-
			message: "#^Attribute class Symfony\\\\Component\\\\Validator\\\\Constraints\\\\IdenticalTo does not have the parameter target\\.$#"
			count: 1
			path: src/Command/QuestionResponse/MetricBodyMassIndexQuestionResponse.php

		-
			message: "#^Attribute class Symfony\\\\Component\\\\Validator\\\\Constraints\\\\NotBlank does not have the parameter target\\.$#"
			count: 1
			path: src/Command/QuestionResponse/MetricBodyMassIndexQuestionResponse.php

		-
			message: "#^Method App\\\\Command\\\\QuestionResponse\\\\MultipleChoiceQuestionResponse\\:\\:getChoices\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Command/QuestionResponse/MultipleChoiceQuestionResponse.php



		-
			message: "#^Method App\\\\Command\\\\QuestionnaireSession\\\\UpdateQuestionnaireSession\\:\\:__construct\\(\\) has parameter \\$productCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Command/QuestionnaireSession/UpdateQuestionnaireSession.php

		-
			message: "#^Method App\\\\Command\\\\QuestionnaireSession\\\\UpdateQuestionnaireSession\\:\\:getProductCodes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Command/QuestionnaireSession/UpdateQuestionnaireSession.php

		-
			message: "#^Class App\\\\Command\\\\Translations\\\\ImportTranslations implements generic interface App\\\\Command\\\\CsvAwareInterface but does not specify its types\\: TValue$#"
			count: 1
			path: src/Command/Translations/ImportTranslations.php

		-
			message: "#^Method App\\\\Command\\\\Translations\\\\ImportTranslations\\:\\:getCsvReader\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Command/Translations/ImportTranslations.php

		-
			message: "#^Method App\\\\Command\\\\Translations\\\\ImportTranslations\\:\\:setCsvReader\\(\\) has parameter \\$csvReader with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Command/Translations/ImportTranslations.php

		-
			message: "#^Property App\\\\Command\\\\Translations\\\\ImportTranslations\\:\\:\\$csvReader with generic class League\\\\Csv\\\\Reader does not specify its types\\: TValue$#"
			count: 1
			path: src/Command/Translations/ImportTranslations.php

		-
			message: "#^Parameter \\#1 \\$languageId of method App\\\\Entity\\\\QuestionnaireSession\\:\\:setLanguageId\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/CommandHandler/QuestionnaireSession/UpdateQuestionnaireSessionHandler.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\ExportTranslationsHandler\\:\\:addToCsv\\(\\) has parameter \\$csvHeaderData with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CommandHandler/Translations/ExportTranslationsHandler.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\ExportTranslationsHandler\\:\\:addToCsv\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CommandHandler/Translations/ExportTranslationsHandler.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\ExportTranslationsHandler\\:\\:generateCsvHeaderData\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CommandHandler/Translations/ExportTranslationsHandler.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\ExportTranslationsHandler\\:\\:getQuestionTranslationByLocaleCode\\(\\) has parameter \\$questionTranslations with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection but does not specify its types\\: TKey, T$#"
			count: 1
			path: src/CommandHandler/Translations/ExportTranslationsHandler.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\ImportTranslationsHandler\\:\\:__invoke\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CommandHandler/Translations/ImportTranslationsHandler.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\QuestionTranslationBuilder\\:\\:setNewQuestionChoiceValues\\(\\) has parameter \\$choices with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CommandHandler/Translations/QuestionTranslationBuilder.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\QuestionTranslationBuilder\\:\\:setNewQuestionTranslationValues\\(\\) has parameter \\$questionValues with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CommandHandler/Translations/QuestionTranslationBuilder.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\QuestionTranslationBuilder\\:\\:splitRows\\(\\) has parameter \\$row with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CommandHandler/Translations/QuestionTranslationBuilder.php

		-
			message: "#^Method App\\\\CommandHandler\\\\Translations\\\\QuestionTranslationBuilder\\:\\:splitRows\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/CommandHandler/Translations/QuestionTranslationBuilder.php

		-
			message: "#^Method App\\\\Comparator\\\\QuestionTranslationComparator\\:\\:getDefaultQuestionTranslation\\(\\) has parameter \\$questionTranslations with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection but does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Comparator/QuestionTranslationComparator.php

		-
			message: "#^Method App\\\\Console\\\\DeviantQuestionTranslationCommand\\:\\:checkQuestionTranslations\\(\\) has parameter \\$deviantQuestions with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Console/DeviantQuestionTranslationCommand.php

		-
			message: "#^Method App\\\\Console\\\\DeviantQuestionTranslationCommand\\:\\:checkQuestionTranslations\\(\\) has parameter \\$questionTranslations with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection but does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Console/DeviantQuestionTranslationCommand.php

		-
			message: "#^Method App\\\\Console\\\\DeviantQuestionTranslationCommand\\:\\:getQuestionTranslations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Console/DeviantQuestionTranslationCommand.php

		-
			message: "#^Method App\\\\Controller\\\\AbstractController\\:\\:getSerializationContext\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Controller/AbstractController.php

		-
			message: "#^Method App\\\\Controller\\\\AbstractController\\:\\:getViolationsFromException\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Controller/AbstractController.php

		-
			message: "#^Parameter \\#1 \\$input of static method Symfony\\\\Component\\\\Yaml\\\\Yaml\\:\\:parse\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/Controller/DocumentationController.php

		-
			message: "#^Cannot call method getId\\(\\) on App\\\\Entity\\\\Question\\|null\\.$#"
			count: 1
			path: src/Controller/DownloadQuestionAnswerFileController.php

		-
			message: "#^Parameter \\#2 \\$to of function stream_copy_to_stream expects resource, resource\\|false given\\.$#"
			count: 1
			path: src/Controller/DownloadQuestionAnswerFileController.php

		-
			message: "#^Cannot call method removeElement\\(\\) on Doctrine\\\\Common\\\\Collections\\\\Collection\\|null\\.$#"
			count: 1
			path: src/Entity/Question.php

		-
			message: "#^Method App\\\\Entity\\\\Question\\:\\:getQuestionTranslations\\(\\) should return Doctrine\\\\Common\\\\Collections\\\\Collection\\<int, App\\\\Entity\\\\QuestionTranslation\\> but returns Doctrine\\\\Common\\\\Collections\\\\Collection\\|null\\.$#"
			count: 1
			path: src/Entity/Question.php

		-
			message: "#^Property App\\\\Entity\\\\Question\\:\\:\\$questionSections with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Entity/Question.php

		-
			message: "#^Property App\\\\Entity\\\\Question\\:\\:\\$questionTranslations \\(Doctrine\\\\Common\\\\Collections\\\\Collection\\|null\\) does not accept array\\<int, App\\\\Entity\\\\QuestionTranslation\\>\\|Doctrine\\\\Common\\\\Collections\\\\Collection\\.$#"
			count: 1
			path: src/Entity/Question.php

		-
			message: "#^Property App\\\\Entity\\\\Question\\:\\:\\$questionTranslations with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Entity/Question.php

		-
			message: "#^Property App\\\\Entity\\\\Question\\:\\:\\$questionnaireResponses with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Entity/Question.php

		-
			message: "#^Method App\\\\Entity\\\\QuestionChoice\\:\\:setId\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: src/Entity/QuestionChoice.php

		-
			message: "#^Property App\\\\Entity\\\\QuestionChoice\\:\\:\\$questionnaireResponseChoices with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Entity/QuestionChoice.php

		-
			message: "#^Method App\\\\Entity\\\\QuestionType\\:\\:setId\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: src/Entity/QuestionType.php

		-
			message: "#^Method App\\\\Entity\\\\QuestionnaireResponse\\:\\:getUploadFile\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/QuestionnaireResponse.php

		-
			message: "#^Method App\\\\Entity\\\\QuestionnaireResponse\\:\\:setUploadFile\\(\\) has parameter \\$uploadFile with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/QuestionnaireResponse.php

		-
			message: "#^Property App\\\\Entity\\\\QuestionnaireResponse\\:\\:\\$questionnaireResponseChoices with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Entity/QuestionnaireResponse.php

		-
			message: "#^Property App\\\\Entity\\\\QuestionnaireResponse\\:\\:\\$uploadFile type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Entity/QuestionnaireResponse.php

		-
			message: "#^Method App\\\\Entity\\\\Section\\:\\:setId\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: src/Entity/Section.php

		-
			message: "#^Property App\\\\Entity\\\\Section\\:\\:\\$questionSections with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Entity/Section.php

		-
			message: "#^Class App\\\\EventSubscriber\\\\DeserializedCommand\\\\LocaleCodeAwareSubscriber extends generic class App\\\\EventSubscriber\\\\DeserializedCommand\\\\AbstractDeserializationSubscriber but does not specify its types\\: T$#"
			count: 1
			path: src/EventSubscriber/DeserializedCommand/LocaleCodeAwareSubscriber.php

		-
			message: "#^Method App\\\\Exception\\\\QuestionNotFoundException\\:\\:__construct\\(\\) has parameter \\$headers with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Exception/QuestionNotFoundException.php

		-
			message: "#^Method App\\\\Exception\\\\QuestionnaireSessionNotFilledException\\:\\:__construct\\(\\) has parameter \\$headers with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Exception/QuestionnaireSessionNotFilledException.php

		-
			message: "#^Method App\\\\Exception\\\\QuestionnaireSessionNotFoundException\\:\\:__construct\\(\\) has parameter \\$headers with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Exception/QuestionnaireSessionNotFoundException.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionSectionRepository\\:\\:getSectionsByType\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/QuestionSectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionTypesRepository\\:\\:findBySlug\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/Repository/QuestionTypesRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionnaireResponseRepository\\:\\:countBySessionId\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Repository/QuestionnaireResponseRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionnaireResponseRepository\\:\\:countBySessionId\\(\\) has parameter \\$sessionID with no type specified\\.$#"
			count: 1
			path: src/Repository/QuestionnaireResponseRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionnaireResponseRepository\\:\\:findBySessionId\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Repository/QuestionnaireResponseRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionnaireResponseRepository\\:\\:findBySessionId\\(\\) has parameter \\$sessionID with no type specified\\.$#"
			count: 1
			path: src/Repository/QuestionnaireResponseRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionnaireResponseRepository\\:\\:findBySessionIdFull\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Repository/QuestionnaireResponseRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionnaireResponseRepository\\:\\:findBySessionIdFull\\(\\) has parameter \\$sessionID with no type specified\\.$#"
			count: 1
			path: src/Repository/QuestionnaireResponseRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionnaireResponseRepository\\:\\:findBySessionIdFull\\(\\) has parameter \\$sessionLanguage with no type specified\\.$#"
			count: 1
			path: src/Repository/QuestionnaireResponseRepository.php

		-
			message: "#^Parameter \\#1 \\$language of method App\\\\Entity\\\\QuestionnaireSession\\:\\:setLanguage\\(\\) expects App\\\\Entity\\\\Language, App\\\\Entity\\\\Language\\|null given\\.$#"
			count: 1
			path: src/Repository/QuestionnaireSessionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionsRepository\\:\\:buildQuery\\(\\) has parameter \\$filters with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/QuestionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionsRepository\\:\\:buildQuery\\(\\) has parameter \\$order with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/QuestionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionsRepository\\:\\:countQuestions\\(\\) has parameter \\$filters with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/QuestionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionsRepository\\:\\:countQuestions\\(\\) has parameter \\$order with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/QuestionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionsRepository\\:\\:findQuestions\\(\\) has parameter \\$filters with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/QuestionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionsRepository\\:\\:findQuestions\\(\\) has parameter \\$order with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/QuestionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionsRepository\\:\\:findQuestions\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/QuestionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\QuestionsRepository\\:\\:getNextPublicId\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Repository/QuestionsRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:buildQuery\\(\\) has parameter \\$filters with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:buildQuery\\(\\) has parameter \\$order with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:countSections\\(\\) has parameter \\$filters with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:countSections\\(\\) has parameter \\$order with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:findSections\\(\\) has parameter \\$filters with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:findSections\\(\\) has parameter \\$order with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:findSections\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:getDefaultSections\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:getOtherSections\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Repository\\\\SectionRepository\\:\\:getSection\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Repository/SectionRepository.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopeBasedAuthorizationChecker\\:\\:isGranted\\(\\) has parameter \\$routePrefixes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopeBasedAuthorizationChecker.php

		-
			message: "#^Parameter \\#1 \\$search of function str_replace expects array\\|string, array\\|null given\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopeBasedAuthorizationChecker.php

		-
			message: "#^Parameter \\#2 \\$needle of function str_starts_with expects string, string\\|null given\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopeBasedAuthorizationChecker.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopeBasedAuthorizationCheckerInterface\\:\\:isGranted\\(\\) has parameter \\$routePrefixes with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopeBasedAuthorizationCheckerInterface.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopesProvider.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopesProvider\\:\\:getOperationSpecificationByOperationId\\(\\) has parameter \\$pathSpecifications with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopesProvider.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopesProvider\\:\\:getOperationSpecificationByOperationId\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopesProvider.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopesProvider\\:\\:getScopes\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopesProvider.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopesProvider\\:\\:getScopesByOperationId\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopesProvider.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopesProvider\\:\\:getScopesFromSecuritySpecification\\(\\) has parameter \\$operationSpecification with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopesProvider.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopesProvider\\:\\:getScopesFromSecuritySpecification\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopesProvider.php

		-
			message: "#^Method App\\\\Security\\\\OpenApi\\\\ScopesProviderInterface\\:\\:getScopesByOperationId\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Security/OpenApi/ScopesProviderInterface.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\AbstractCustomField\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/AbstractCustomField.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\AbstractCustomField\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/AbstractCustomField.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\AbstractCustomField\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/AbstractCustomField.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\CustomFieldInterface\\:\\:add\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/CustomFieldInterface.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\CustomFieldInterface\\:\\:add\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/CustomFieldInterface.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\CustomFieldInterface\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/CustomFieldInterface.php

		-
			message: "#^Method App\\\\Serializer\\\\CustomField\\\\QuestionnaireSession\\\\Questionnaire\\:\\:supports\\(\\) has parameter \\$context with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/CustomField/QuestionnaireSession/Questionnaire.php

		-
			message: "#^Method App\\\\Serializer\\\\Denormalizer\\\\QuestionSectionDenormalizer\\:\\:denormalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/Denormalizer/QuestionSectionDenormalizer.php

		-
			message: "#^Constant App\\\\Serializer\\\\Denormalizer\\\\QuestionnaireSessionDenormalizer\\:\\:FILTER_QUESTIONNAIRE_BY_SECTION_PARAM is unused\\.$#"
			count: 1
			path: src/Serializer/Denormalizer/QuestionnaireSessionDenormalizer.php

		-
			message: "#^Method App\\\\Serializer\\\\ObjectNormalizer\\:\\:normalize\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Serializer/ObjectNormalizer.php

		-
			message: "#^Method App\\\\Service\\\\QuestionsGetServices\\:\\:getQuestions\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Service/QuestionsGetServices.php

		-
			message: "#^Method App\\\\Service\\\\ReassignQuestionToSectionService\\:\\:handle\\(\\) has parameter \\$dbQuestionSection with no type specified\\.$#"
			count: 1
			path: src/Service/ReassignQuestionToSectionService.php

		-
			message: "#^Method App\\\\Service\\\\ReassignQuestionToSectionService\\:\\:handle\\(\\) has parameter \\$question with no type specified\\.$#"
			count: 1
			path: src/Service/ReassignQuestionToSectionService.php

		-
			message: "#^Attribute class JetBrains\\\\PhpStorm\\\\Deprecated does not exist\\.$#"
			count: 2
			path: src/Service/S3/S3Service.php

		-
			message: "#^Method App\\\\Service\\\\S3\\\\S3Service\\:\\:deprecatedGetObjectFromBucket\\(\\) has parameter \\$filename with no type specified\\.$#"
			count: 1
			path: src/Service/S3/S3Service.php

		-
			message: "#^Method App\\\\Service\\\\S3\\\\S3Service\\:\\:deprecatedUploadFileToBucket\\(\\) has parameter \\$file with no type specified\\.$#"
			count: 1
			path: src/Service/S3/S3Service.php

		-
			message: "#^Parameter \\#2 \\$fileName of method App\\\\Service\\\\S3\\\\S3Service\\:\\:getFileName\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/Service/S3/S3Service.php

		-
			message: "#^Method App\\\\Service\\\\SectionsGetServices\\:\\:getSections\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Service/SectionsGetServices.php

		-
			message: "#^Method App\\\\Validator\\\\QuestionChoiceTextNotBlank\\:\\:__construct\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Validator/QuestionChoiceTextNotBlank.php

		-
			message: "#^Method App\\\\Validator\\\\QuestionTranslationStructureValidator\\:\\:getDeviantTranslations\\(\\) has parameter \\$value with generic interface Doctrine\\\\Common\\\\Collections\\\\Collection but does not specify its types\\: TKey, T$#"
			count: 1
			path: src/Validator/QuestionTranslationStructureValidator.php

		-
			message: "#^Method App\\\\Validator\\\\QuestionTranslationStructureValidator\\:\\:getDeviantTranslations\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Validator/QuestionTranslationStructureValidator.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\ContextStorage\\:\\:__construct\\(\\) has parameter \\$parameters with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/ContextStorage.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:getResponse\\(\\) has parameter \\$key with no type specified\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:jsonRequest\\(\\) has parameter \\$jsonRequestBody with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:jsonRequest\\(\\) has parameter \\$server with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:request\\(\\) has parameter \\$parameters with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:request\\(\\) has parameter \\$server with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Property App\\\\Tests\\\\Behat\\\\HttpClient\\:\\:\\$responses type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Behat/HttpClient.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\AbstractWebTestCase\\:\\:getResponseBody\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/AbstractWebTestCase.php

		-
			message: "#^Cannot call method getCode\\(\\) on App\\\\Entity\\\\Product\\|false\\.$#"
			count: 2
			path: tests/Functional/Api/QuestionnaireSession/CreateQuestionnaireTest.php

		-
			message: "#^Cannot call method isFinished\\(\\) on App\\\\Entity\\\\QuestionnaireSession\\|null\\.$#"
			count: 1
			path: tests/Functional/Api/QuestionnaireSession/FinalizeQuestionnaireTest.php

		-
			message: "#^Cannot call method getContent\\(\\) on App\\\\Entity\\\\QuestionnaireResponse\\|false\\.$#"
			count: 2
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Cannot call method getQuestionnaireResponseChoices\\(\\) on App\\\\Entity\\\\QuestionnaireResponse\\|false\\.$#"
			count: 3
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\QuestionnaireSession\\\\QuestionResponse\\\\AddQuestionResponseTest\\:\\:provideInvalidRequestBodies\\(\\) is unused\\.$#"
			count: 1
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\QuestionnaireSession\\\\QuestionResponse\\\\AddQuestionResponseTest\\:\\:provideInvalidRequestBodies\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\QuestionnaireSession\\\\QuestionResponse\\\\AddQuestionResponseTest\\:\\:provideRequestBodies\\(\\) is unused\\.$#"
			count: 1
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\QuestionnaireSession\\\\QuestionResponse\\\\AddQuestionResponseTest\\:\\:provideRequestBodies\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\QuestionnaireSession\\\\QuestionResponse\\\\AddQuestionResponseTest\\:\\:testCanAddSimpleQuestionResponses\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\QuestionnaireSession\\\\QuestionResponse\\\\AddQuestionResponseTest\\:\\:testCanValidateInvalidResponses\\(\\) has parameter \\$data with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Parameter \\#1 \\$language of method App\\\\Entity\\\\QuestionnaireSession\\:\\:setLanguage\\(\\) expects App\\\\Entity\\\\Language, App\\\\Entity\\\\Language\\|null given\\.$#"
			count: 1
			path: tests/Functional/Api/QuestionnaireSession/QuestionResponse/AddQuestionResponseTest.php

		-
			message: "#^Parameter \\#1 \\$actualJson of method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJson\\(\\) expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Api/QuestionnaireSession/UpdateQuestionnaireTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Api/QuestionnaireSession/UpdateQuestionnaireTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Questions\\\\UpdateQuestionTest\\:\\:filterTimestampsFromArray\\(\\) has parameter \\$array with no type specified\\.$#"
			count: 1
			path: tests/Functional/Api/Questions/UpdateQuestionTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Questions\\\\UpdateQuestionTest\\:\\:filterTimestampsFromArray\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Questions/UpdateQuestionTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Api\\\\Questions\\\\UpdateQuestionTest\\:\\:testUpdateQuestion\\(\\) has parameter \\$questionsLanguagesRequestBody with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Api/Questions/UpdateQuestionTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Api/Questions/UpdateQuestionTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Controller/LanguageTest.php

		-
			message: "#^Cannot call method findByLocaleCode\\(\\) on object\\|null\\.$#"
			count: 8
			path: tests/Functional/Controller/QuestionTest.php

		-
			message: "#^Cannot call method findBySlug\\(\\) on object\\|null\\.$#"
			count: 8
			path: tests/Functional/Controller/QuestionTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Controller\\\\QuestionTest\\:\\:provideClientOptions\\(\\) is unused\\.$#"
			count: 1
			path: tests/Functional/Controller/QuestionTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Controller\\\\QuestionTest\\:\\:provideClientOptions\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Controller/QuestionTest.php

		-
			message: "#^Method App\\\\Tests\\\\Functional\\\\Controller\\\\QuestionTest\\:\\:testQuestionSingleChoiceCreate\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Controller/QuestionTest.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 9
			path: tests/Functional/Controller/QuestionTest.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:checksum\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:copy\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:createDirectory\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:move\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:publicUrl\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:temporaryUrl\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:write\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:writeStream\\(\\) has parameter \\$config with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Return type \\(void\\) of method App\\\\Tests\\\\Mocks\\\\FilesystemOperator\\:\\:readStream\\(\\) should be compatible with return type \\(resource\\) of method League\\\\Flysystem\\\\FilesystemReader\\:\\:readStream\\(\\)$#"
			count: 2
			path: tests/Mocks/FilesystemOperator.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:__call\\(\\) has parameter \\$arguments with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:copy\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:copyAsync\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:createPresignedRequest\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:deleteMatchingObjects\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:deleteMatchingObjects\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:deleteMatchingObjectsAsync\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:doesObjectExist\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:doesObjectExistV2\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:downloadBucket\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:downloadBucket\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:downloadBucketAsync\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:getCommand\\(\\) has parameter \\$args with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:getIterator\\(\\) has parameter \\$args with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:getPaginator\\(\\) has parameter \\$args with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:getWaiter\\(\\) has parameter \\$args with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:registerStreamWrapper\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:registerStreamWrapperV2\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:upload\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:uploadAsync\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:uploadDirectory\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:uploadDirectory\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:uploadDirectoryAsync\\(\\) has parameter \\$options with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Mocks\\\\S3Client\\:\\:waitUntil\\(\\) has parameter \\$args with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Mocks/S3Client.php

		-
			message: "#^Method App\\\\Tests\\\\Unit\\\\Helper\\\\PaginationTest\\:\\:provideLimitInputs\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Unit/Helper/PaginationTest.php

		-
			message: "#^Method App\\\\Tests\\\\Unit\\\\Helper\\\\PaginationTest\\:\\:testCanGetDefaultLimit\\(\\) has parameter \\$input with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Unit/Helper/PaginationTest.php

		-
			message: "#^Call to an undefined method App\\\\Repository\\\\QuestionsRepository\\:\\:expects\\(\\)\\.$#"
			count: 2
			path: tests/Unit/Service/QuestionsGetServiceTest.php

		-
			message: "#^Method App\\\\Tests\\\\Unit\\\\Service\\\\QuestionsGetServiceTest\\:\\:testGetQuestions\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Unit/Service/QuestionsGetServiceTest.php

		-
			message: "#^Call to an undefined method App\\\\Repository\\\\SectionRepository\\:\\:expects\\(\\)\\.$#"
			count: 2
			path: tests/Unit/Service/SectionGetServiceTest.php

		-
			message: "#^Method App\\\\Tests\\\\Unit\\\\Service\\\\SectionGetServiceTest\\:\\:testGetSections\\(\\) has no return type specified\\.$#"
			count: 1
			path: tests/Unit/Service/SectionGetServiceTest.php

		-
			message: "#^Class App\\\\Tests\\\\Unit\\\\Validator\\\\ImportQuestionsStructureValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Unit/Validator/ImportQuestionsStructureValidatorTest.php

		-
			message: "#^Parameter \\#1 \\$csvReader of method App\\\\Command\\\\Translations\\\\ImportTranslations\\:\\:setCsvReader\\(\\) expects League\\\\Csv\\\\Reader\\<array\\>, League\\\\Csv\\\\Reader\\<array\\<string, string\\>\\> given\\.$#"
			count: 1
			path: tests/Unit/Validator/ImportQuestionsStructureValidatorTest.php

		-
			message: "#^Class App\\\\Tests\\\\Unit\\\\Validator\\\\QuestionChoiceFieldsNotBlankValidatorTest extends generic class Symfony\\\\Component\\\\Validator\\\\Test\\\\ConstraintValidatorTestCase but does not specify its types\\: T$#"
			count: 1
			path: tests/Unit/Validator/QuestionChoiceFieldsNotBlankValidatorTest.php

		-
			message: "#^Method App\\\\Tests\\\\Unit\\\\Validator\\\\QuestionChoiceFieldsNotBlankValidatorTest\\:\\:provideQuestionTypeSlugWithValidation\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Unit/Validator/QuestionChoiceFieldsNotBlankValidatorTest.php

		-
			message: "#^Method App\\\\Tests\\\\Unit\\\\Validator\\\\QuestionChoiceFieldsNotBlankValidatorTest\\:\\:provideQuestionTypeSlugWithoutValidation\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: tests/Unit/Validator/QuestionChoiceFieldsNotBlankValidatorTest.php
